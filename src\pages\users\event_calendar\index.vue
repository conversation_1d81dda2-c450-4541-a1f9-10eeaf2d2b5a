<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '赛事日历',
  },
}
</route>

<template>
  <view class="event-calendar-container">
    <!-- 日历头部 -->
    <view class="calendar-header">
      <view class="month-year">
        <wd-icon name="arrow-left" size="20px" color="#666" @click="prevMonth" />
        <text class="month-text">{{ currentMonthText }}</text>
        <wd-icon name="arrow-right" size="20px" color="#666" @click="nextMonth" />
      </view>
    </view>
    <!-- 星期标题 -->
    <view class="week-header">
      <view class="week-item" v-for="week in weekDays" :key="week">{{ week }}</view>
    </view>
    <!-- 日历网格 -->
    <view class="calendar-grid">
      <view
        class="calendar-day"
        :class="{
          'other-month': day.isOtherMonth,
          today: day.isToday,
          'has-event': day.hasEvent,
          selected: day.isSelected,
        }"
        v-for="day in calendarDays"
        :key="day.key"
        @click="selectDate(day)"
      >
        <text class="day-number">{{ day.isToday ? '今' : day.day }}</text>
        <!-- <view class="event-dot" v-if="day.hasEvent"></view> -->
      </view>
    </view>

    <!-- 赛事列表标题 -->
    <view class="event-list-header">
      <text class="section-title">赛事列表</text>
    </view>

    <!-- 赛事列表 -->
    <view class="event-list">
      <view
        class="event-card"
        v-for="event in eventList"
        :key="event.id"
        @click="goToEventDetail(event)"
      >
        <image class="event-image" :src="event.image" mode="aspectFill" />
        <view class="event-date">{{ event.date }}</view>
        <view class="event-content">
          <view class="event-title">{{ event.title }}</view>
          <view class="event-location">{{ event.location }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'

// 星期标题
const weekDays = ['一', '二', '三', '四', '五', '六', '日']

// 当前日期
const currentDate = ref(new Date())
const selectedDate = ref(new Date())

// 当前月份文本
const currentMonthText = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year} / ${month.toString().padStart(2, '0')}`
})

// 生成日历数据
const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 获取第一天是星期几（0=周日，1=周一...）
  const firstDayWeek = firstDay.getDay() === 0 ? 7 : firstDay.getDay()

  const days = []

  // 添加上个月的日期
  const prevMonth = new Date(year, month - 1, 0)
  for (let i = firstDayWeek - 1; i > 0; i--) {
    const day = prevMonth.getDate() - i + 1
    days.push({
      key: `prev-${day}`,
      day,
      isOtherMonth: true,
      isToday: false,
      hasEvent: false,
      isSelected: false,
      date: new Date(year, month - 1, day),
    })
  }

  // 添加当月的日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(year, month, day)
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    const isSelected = date.toDateString() === selectedDate.value.toDateString()

    // 检查是否有事件（示例：3号、6号、30号有事件）
    const hasEvent = [3, 6, 30].includes(day) && month === 7 && year === 2025

    days.push({
      key: `current-${day}`,
      day,
      isOtherMonth: false,
      isToday,
      hasEvent,
      isSelected,
      date,
    })
  }

  // 添加下个月的日期，补齐6行
  const remainingDays = 42 - days.length
  for (let day = 1; day <= remainingDays; day++) {
    days.push({
      key: `next-${day}`,
      day,
      isOtherMonth: true,
      isToday: false,
      hasEvent: false,
      isSelected: false,
      date: new Date(year, month + 1, day),
    })
  }

  return days
})

// 事件列表数据
const eventList = ref([
  {
    id: 1,
    title: '大邑安仁半程马拉松2025.11.1',
    date: '报名倒计时：3 天 23 小时 40 分 18 秒',
    location: '四川省成都市大邑县',
    image: 'https://dummyimage.com/264x264/3c9cff/fff',
  },
  {
    id: 2,
    title: '大邑安仁半程马拉松2025.11.1',
    date: '报名倒计时：3 天 23 小时 40 分 18 秒',
    location: '四川省成都市大邑县',
    image: 'https://dummyimage.com/264x264/3c9cff/fff',
  },
  {
    id: 3,
    title: '大邑安仁半程马拉松2025.11.1',
    date: '报名倒计时：3 天 23 小时 40 分 18 秒',
    location: '四川省成都市大邑县',
    image: 'https://dummyimage.com/264x264/3c9cff/fff',
  },
])

// 切换月份
const prevMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

// 选择日期
const selectDate = (day: any) => {
  if (!day.isOtherMonth) {
    selectedDate.value = day.date
  }
}

// 跳转到事件详情
const goToEventDetail = (event: any) => {
  console.log('跳转到事件详情:', event)
  // 这里可以添加跳转逻辑
}

// 初始化
onMounted(() => {
  // 设置为2025年8月
  currentDate.value = new Date(2025, 7, 1) // 月份从0开始，7表示8月
  selectedDate.value = new Date(2025, 7, 3) // 默认选中3号
})
</script>

<style lang="scss" scoped>
.event-calendar-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 10rpx;
}

// 日历头部
.calendar-header {
  background: #fff;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  border-radius: 12rpx;

  .month-year {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40rpx;

    .month-text {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      min-width: 200rpx;
      text-align: center;
    }
  }
}

// 星期标题
.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;

  .week-item {
    padding: 24rpx 0;
    text-align: center;
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }
}

// 日历网格
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #fff;

  .calendar-day {
    position: relative;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;

    &:nth-child(7n) {
      border-right: none;
    }

    &.other-month {
      .day-number {
        color: #ccc;
      }
    }
    .day-number {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
    }

    &.today {
      .day-number {
        border-radius: 50%;
        border: 1rpx solid #000000;
      }
    }

    &.selected {
      //   background: #f0f9ff;

      .day-number {
        background: #1a1a1a !important;
        border: none !important;
        color: #c5f355 !important;
        border-radius: 50%;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
      }
    }

    &.has-event {
      .day-number {
        background: #c5f355;
        border-radius: 50%;
        border: 1rpx solid #000000;
      }
      .event-dot {
        position: absolute;
        bottom: 8rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 12rpx;
        height: 12rpx;
        background: #ff6b6b;
        border-radius: 50%;
      }

      &.selected .event-dot {
        background: #fff;
      }
    }

    .day-number {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

// 赛事列表标题
.event-list-header {
  padding: 32rpx 40rpx 24rpx;
  background: #f5f5f5;

  .section-title {
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
  }
}

// 赛事列表
.event-list {
  padding: 0 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;

  .event-card {
    background: #fff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition:
      transform 0.2s,
      box-shadow 0.2s;
    position: relative;

    .event-image {
      width: 100%;
      height: 300rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .event-date {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 48rpx;
      background: #000000;
      border-radius: 11rpx 11rpx 0rpx 0rpx;
      opacity: 0.68;
      font-weight: 300;
      font-size: 24rpx;
      color: #c5f355;
      text-align: center;
      line-height: 48rpx;
    }

    .event-content {
      padding: 16rpx 32rpx;
      .event-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
        line-height: 1.4;
      }

      .event-location {
        font-size: 26rpx;
        color: #999;
      }
    }
  }
}
</style>
