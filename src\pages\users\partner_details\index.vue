<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '详情',
  },
}
</route>

<template>
  <view class="partner-detail">
    <!-- 轮播图 -->
    <view class="swiper-container">
      <wd-swiper
        :list="swiperImages"
        :autoplay="false"
        :height="375"
        v-model:current="current"
        @click="handleSwiperClick"
        @change="onSwiperChange"
      >
        <template #indicator="{ current, total }">
          <view class="custom-indicator">{{ current + 1 }}/{{ total }}</view>
        </template>
      </wd-swiper>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 用户信息 -->
      <view class="user-section">
        <view class="user-info">
          <image
            class="avatar"
            :src="partnerInfo.avatar || '/static/images/login-female-default.png'"
            mode="aspectFill"
          />
          <text class="nickname">{{ partnerInfo.nickname }}</text>
        </view>
      </view>

      <!-- 价格和标签 -->
      <view class="price-section">
        <view class="price-info">
          <text class="price">¥{{ partnerInfo.price }}</text>
          <view class="tags">
            <text class="tag tag-house">{{ partnerInfo.houseTag }}</text>
            <text class="tag tag-gender">{{ partnerInfo.genderTag }}</text>
            <text class="tag tag-limit">{{ partnerInfo.limitTag }}</text>
          </view>
        </view>
      </view>

      <!-- 活动信息 -->
      <view class="activity-section">
        <view class="activity-item">
          <image class="icon" src="/static/images/icon-saishi.png" mode="widthFix" />
          <text class="text">{{ partnerInfo.location }}</text>
        </view>
        <view class="activity-item">
          <image class="icon" src="/static/images/icon-jiudian.png" mode="widthFix" />
          <text class="text">{{ partnerInfo.hotel }}</text>
        </view>
        <view class="activity-item">
          <image class="icon" src="/static/images/icon-time-2.png" mode="widthFix" />
          <text class="text">{{ partnerInfo.time }}</text>
        </view>
      </view>

      <!-- 描述信息 -->
      <view class="description-section">
        <text class="title">
          {{ partnerInfo.title }}
        </text>

        <view class="description-header">
          <text class="header-title">活动描述</text>
        </view>

        <view class="description-content">
          <text class="content-text">
            {{ partnerInfo.description }}
          </text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-buttons">
      <button class="btn btn-share" @click="handleShare">分享活动</button>
      <button class="btn btn-contact" @click="handleContact">联系TA</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const current = ref(0)

// 轮播图数据
const swiperImages = ref([
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
  'https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=400&h=300&fit=crop',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
])

// 搭子信息
const partnerInfo = reactive({
  avatar: 'https://dummyimage.com/100x100/3c9cff/fff',
  nickname: '美丽人生',
  price: 60,
  houseTag: '拼房',
  genderTag: '房找人',
  limitTag: '限1女',
  location: '2025大邑安仁半程马拉松',
  hotel: '亚朵酒店/双床标间',
  time: '25/07/19 12:00:00—25/07/19 12:00:00',
  title: '房找人，限1女，A房费，60元每人,天府跑团',
  description:
    '本人女生，10月参加大邑安仁半程马拉松，房已好，亚朵双床标间，希望找一名女生房费，有意者请联系。',
})

// 轮播图点击事件
const handleSwiperClick = (e: any) => {
  console.log('轮播图点击', e)
}

// 轮播图切换事件
const onSwiperChange = (e: any) => {
  current.value = e.detail.current
}

// 分享活动
const handleShare = () => {
  toast.success('分享功能开发中')
}

// 联系TA
const handleContact = () => {
  toast.success('联系功能开发中')
}

// 页面加载时的逻辑
onLoad((options: any) => {
  // 这里可以根据传入的参数加载具体的搭子信息
  console.log('页面参数:', options)
})
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.partner-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 200rpx;
}

// 轮播图样式
.swiper-container {
  position: relative;

  .custom-indicator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 84rpx;
    height: 49rpx;
    font-size: 26rpx;
    font-weight: 400;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 25rpx;
    z-index: 10;
  }
}

// 内容区域
.content-wrapper {
  background-color: #ffffff;
  margin-top: -20rpx;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx 30rpx;
  position: relative;
  z-index: 5;
}

// 用户信息区域
.user-section {
  margin-bottom: 30rpx;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }

    .nickname {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }
}

// 价格和标签区域
.price-section {
  margin-bottom: 40rpx;

  .price-info {
    display: flex;
    align-items: center;
    // justify-content: space-between;

    .price {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff4757;
    }
    .tags {
      display: flex;
      gap: 16rpx;
      margin-left: 20rpx;

      .tag {
        padding: 8rpx 16rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        color: #ffffff;

        &.tag-house {
          background-color: #c5f355;
          color: #333333;
        }

        &.tag-gender {
          border: 1px solid #1a1a1a;
          color: #1a1a1a;
        }

        &.tag-limit {
          border: 1px solid #1a1a1a;
          color: #1a1a1a;
        }
      }
    }
  }
}

// 活动信息区域
.activity-section {
  margin-bottom: 40rpx;

  .activity-item {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.4;
    }
  }
}

// 描述区域
.description-section {
  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    line-height: 1.5;
    margin-bottom: 30rpx;
    display: block;
  }

  .description-header {
    margin-bottom: 20rpx;

    .header-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      &::after {
        content: '';
        display: block;
        width: 110rpx;
        height: 10rpx;
        background: #c5f355;
        border-radius: 3rpx;
        margin-top: -10rpx;
      }
    }
  }

  .description-content {
    .content-text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
    }
  }
}

// 底部按钮
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
  z-index: 100;

  .btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &.btn-share {
      background-color: #f8f8f8;
      color: #333333;

      &:active {
        background-color: #e8e8e8;
      }
    }

    &.btn-contact {
      background-color: #c5f355;
      color: #333333;

      &:active {
        background-color: #b8e84a;
      }
    }
  }
}
</style>
